package config

import (
	"time"
)

type JWTConfig struct {
	SigningKey  string `mapstructure:"signing-key"`
	ExpiresTime string `mapstructure:"expires-time"`
	BufferTime  string `mapstructure:"buffer-time"`
	Issuer      string `mapstructure:"issuer"`
}

func (j *JWTConfig) GetExpiresDuration() time.Duration {
	duration, err := time.ParseDuration(j.ExpiresTime)
	if err != nil {
		return 7 * 24 * time.Hour // Default to 7 days
	}
	return duration
}

func (j *JWTConfig) GetBufferDuration() time.Duration {
	duration, err := time.ParseDuration(j.BufferTime)
	if err != nil {
		return 24 * time.Hour // Default to 1 day
	}
	return duration
}

func (j *JWTConfig) GetSigningKey() []byte {
	return []byte(j.Signing<PERSON>ey)
}
