package config

import (
	"strconv"
	"strings"
)

type SystemConfig struct {
	Env           string `mapstructure:"env"`
	Addr          string `mapstructure:"addr"`
	RouterPrefix  string `mapstructure:"router-prefix"`
	GraphQLPrefix string `mapstructure:"graphql-prefix"`
}

func (s *SystemConfig) GetPort() int {
	port, err := strconv.Atoi(s.Addr)
	if err != nil {
		return 8080
	}
	return port
}

func (s *SystemConfig) IsProduction() bool {
	return strings.ToLower(s.Env) == "production"
}

func (s *SystemConfig) IsDevelopment() bool {
	return strings.ToLower(s.Env) == "local" || strings.ToLower(s.Env) == "development"
}
