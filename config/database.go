package config

import (
	"fmt"
	"strconv"
)

type DatabaseConfig struct {
	Path         string `mapstructure:"path"`
	Port         string `mapstructure:"port"`
	Config       string `mapstructure:"config"`
	DbName       string `mapstructure:"db-name"`
	Username     string `mapstructure:"username"`
	Password     string `mapstructure:"password"`
	Prefix       string `mapstructure:"prefix"`
	Singular     bool   `mapstructure:"singular"`
	Engine       string `mapstructure:"engine"`
	MaxIdleConns int    `mapstructure:"max-idle-conns"`
	MaxOpenConns int    `mapstructure:"max-open-conns"`
	LogMode      string `mapstructure:"log-mode"`
	LogZap       bool   `mapstructure:"log-zap"`
}

func (d *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s %s",
		d.Path, d.Port, d.Username, d.Password, d.DbName, d.Config)
}

func (d *DatabaseConfig) GetPort() int {
	port, err := strconv.Atoi(d.Port)
	if err != nil {
		return 5432
	}
	return port
}

func (d *DatabaseConfig) GetMaxIdleConns() int {
	if d.MaxIdleConns <= 0 {
		return 10
	}
	return d.MaxIdleConns
}

func (d *DatabaseConfig) GetMaxOpenConns() int {
	if d.MaxOpenConns <= 0 {
		return 100
	}
	return d.MaxOpenConns
}
