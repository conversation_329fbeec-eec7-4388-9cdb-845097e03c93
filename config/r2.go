package config

import "fmt"

type R2Config struct {
	AccountID       string `mapstructure:"account-id"`
	AccessKeyID     string `mapstructure:"access-key-id"`
	SecretAccessKey string `mapstructure:"secret-access-key"`
	BucketName      string `mapstructure:"bucket-name"`
	Region          string `mapstructure:"region"`
	Endpoint        string `mapstructure:"endpoint"`
}

func (r *R2Config) IsConfigured() bool {
	return r.AccountID != "" && r.AccessKeyID != "" && r.SecretAccessKey != "" && r.BucketName != ""
}

func (r *R2Config) GetRegion() string {
	if r.Region == "" {
		return "auto"
	}
	return r.Region
}

func (r *R2Config) GetEndpoint() string {
	if r.Endpoint == "" {
		return fmt.Sprintf("https://%s.r2.cloudflarestorage.com", r.AccountID)
	}
	return r.Endpoint
}
