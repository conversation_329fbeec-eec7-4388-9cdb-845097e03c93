package config

type ZapConfig struct {
	Level         string `mapstructure:"level"`
	Format        string `mapstructure:"format"`
	Prefix        string `mapstructure:"prefix"`
	Director      string `mapstructure:"director"`
	EncodeLevel   string `mapstructure:"encode-level"`
	StacktraceKey string `mapstructure:"stacktrace-key"`
	MaxAge        int    `mapstructure:"max-age"`
	ShowLine      bool   `mapstructure:"show-line"`
	LogInConsole  bool   `mapstructure:"log-in-console"`
}

func (z *ZapConfig) GetLevel() string {
	if z.Level == "" {
		return "info"
	}
	return z.Level
}

func (z *ZapConfig) GetFormat() string {
	if z.Format == "" {
		return "console"
	}
	return z.Format
}

func (z *ZapConfig) GetPrefix() string {
	if z.Prefix == "" {
		return "[xbit-cdn-service]"
	}
	return z.Prefix
}

func (z *ZapConfig) GetDirector() string {
	if z.Director == "" {
		return "log"
	}
	return z.Director
}

func (z *ZapConfig) GetEncodeLevel() string {
	if z.EncodeLevel == "" {
		return "LowercaseColorLevelEncoder"
	}
	return z.EncodeLevel
}

func (z *ZapConfig) GetStacktraceKey() string {
	if z.StacktraceKey == "" {
		return "stacktrace"
	}
	return z.StacktraceKey
}

func (z *ZapConfig) GetMaxAge() int {
	if z.MaxAge <= 0 {
		return 0
	}
	return z.MaxAge
}
