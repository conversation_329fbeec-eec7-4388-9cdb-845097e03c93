# XBIT CDN Service

A high-performance GraphQL-based CDN service built with Go, providing comprehensive file upload and download functionality using Cloudflare R2 storage and CDN optimization. This service is part of the XBIT ecosystem and follows the same architectural patterns as xbit-agent.

## Features

- 🚀 **GraphQL API** with comprehensive file operations
- ☁️ **Cloudflare R2** storage integration with S3-compatible API
- 🌐 **Cloudflare CDN** for optimal performance and caching
- 🔐 **Dual URL support**: Public URLs and Signed URLs for secure access
- 🔒 **JWT Authentication** and authorization middleware
- 📁 **Multi-format support**: Images (JPG, PNG, GIF, WebP) and Videos (MP4, MOV, AVI, WebM)
- 📊 **File metadata management** with tags and custom metadata
- 🔍 **Advanced filtering** and pagination for file listings
- 🐳 **Docker support** with docker-compose for easy deployment
- 📈 **Production-ready** with comprehensive logging and monitoring
- 🔄 **Synchronized architecture** with xbit-agent for consistency

## Tech Stack

- **Language**: Go 1.24+
- **API**: GraphQL with gqlgen code generation
- **Storage**: Cloudflare R2 (S3-compatible)
- **CDN**: Cloudflare CDN with image optimization
- **Database**: PostgreSQL with GORM ORM
- **Authentication**: JWT with middleware
- **Configuration**: Viper with YAML config
- **Logging**: Zap structured logging
- **Containerization**: Docker & Docker Compose
- **Database Migrations**: Atlas

## Project Structure

```
├── cmd/
│   ├── server/          # Application entrypoint
│   └── atlasloader/     # Atlas migration loader
├── internal/
│   ├── config/          # Configuration management
│   ├── controller/      # GraphQL controllers and resolvers
│   │   └── graphql/     # GraphQL handler implementation
│   ├── service/         # Business logic layer
│   ├── repo/           # Data access layer (repositories)
│   ├── model/          # Data models and schemas
│   ├── middleware/      # HTTP middleware
│   └── utils/          # Internal utilities
├── graph/              # GraphQL schema and generated code
│   ├── model/          # Generated GraphQL models
│   └── schema.graphqls # GraphQL schema definition
├── pkg/                # Shared packages
│   ├── auth/           # JWT authentication
│   └── utils/          # Common utilities
├── config/             # Configuration files
├── migrations/         # Database migrations (Atlas)
├── scripts/            # Build and deployment scripts
├── docs/              # Documentation
├── config.yaml        # Main configuration file
├── gqlgen.yml         # GraphQL code generation config
├── atlas.hcl          # Atlas migration config
├── Dockerfile         # Docker configuration
├── docker-compose.yml # Multi-service setup
└── Makefile          # Build and deployment scripts
```

## Quick Start

### Option 1: Docker Compose (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd xbit-cdn-service
   ```

2. **Configure environment variables**
   ```bash
   cp env/local.env.template env/.env.local
   # Edit env/.env.local with your configuration
   ```

3. **Start all services**
   ```bash
   make local-up
   ```

4. **Run database migrations**
   ```bash
   make db-apply
   ```

5. **Access the service**
   - GraphQL Playground: http://localhost:8080/api/cdn-service/graphql/playground
   - GraphQL API: http://localhost:8080/api/cdn-service/graphql
   - Health Check: http://localhost:8080/api/cdn-service/graphql/healthz
   - Authentication: http://localhost:8080/auth/login

### Option 2: Local Development

1. **Prerequisites**
   - Go 1.24+
   - PostgreSQL 15+
   - Atlas CLI (for migrations)

2. **Setup development environment**
   ```bash
   make setup-local
   ```

3. **Configure environment**
   ```bash
   cp env/local.env.template env/.env.local
   # Edit env/.env.local with your configuration
   ```

4. **Install dependencies**
   ```bash
   make install-deps
   ```

5. **Generate GraphQL code**
   ```bash
   make gqlgen
   ```

6. **Start dependencies**
   ```bash
   make local-up
   ```

7. **Run migrations**
   ```bash
   make db-apply
   ```

8. **Start development server**
   ```bash
   make dev-air  # Hot reload with Air
   # or
   make run-local  # Single run
   ```

## Configuration

The service uses YAML configuration files with environment variable overrides. Configuration is managed through `config.yaml` and environment-specific files in the `env/` directory.

### Configuration Files

- `config.yaml` - Main configuration file
- `env/local.env.template` - Local development template
- `env/production.env.template` - Production template

### Key Configuration Sections

#### System Configuration
```yaml
system:
  env: "local"
  addr: "8080"
  router-prefix: "/api/cdn"
  graphql-prefix: "/api/cdn/graphql"
```

#### Database Configuration
```yaml
pgsql:
  path: "127.0.0.1"
  port: "5432"
  db-name: "cdn"
  username: "postgres"
  password: "postgres"
  max-idle-conns: 10
  max-open-conns: 100
```

#### R2 Storage Configuration
```yaml
r2:
  account-id: ""
  access-key-id: ""
  secret-access-key: ""
  bucket-name: ""
  region: "auto"
  endpoint: ""
```

#### CDN Configuration
```yaml
cdn:
  enabled: false
  provider: "cloudflare"
  base-url: ""
  api-token: ""
  zone-id: ""
  domain: ""
  cache-ttl: 3600
  image-optimize: true
```

#### Upload Configuration
```yaml
upload:
  max-file-size: *********  # 100MB
  allowed-extensions: [".jpg", ".png", ".gif", ".webp", ".pdf", ".mp4"]
  signed-url-expiry: 3600  # 1 hour
  chunk-size: 5242880  # 5MB
```

#### JWT Configuration
```yaml
jwt:
  signing-key: "xbit-cdn-service"
  expires-time: "7d"
  buffer-time: "1d"
  issuer: "xbit-cdn-service"
```

### Cloudflare R2 Setup

1. **Create R2 Bucket**
   - Go to Cloudflare Dashboard → R2 Object Storage
   - Create a new bucket
   - Note the bucket name and endpoint

2. **Generate API Tokens**
   - Go to R2 → Manage R2 API tokens
   - Create token with R2:Edit permissions
   - Note the Access Key ID and Secret Access Key

3. **Configure CDN (Optional)**
   - Set up custom domain for your R2 bucket
   - Configure caching rules
   - Update `CDN_BASE_URL` in your environment

## API Documentation

### GraphQL Schema

The service provides a GraphQL API with the following main operations:

#### Queries

```graphql
type Query {
  # Get file by ID
  file(id: ID!): FileMetadata

  # List files with filtering and pagination
  files(filter: FileFilter, pagination: PaginationInput): FileListResponse!

  # Generate download URL
  downloadURL(id: ID!, urlType: URLType = PUBLIC, expiresIn: Int): DownloadResponse!

  # Health check
  health: String!
}
```

#### Mutations

```graphql
type Mutation {
  # Upload file (returns signed URL or direct upload)
  uploadFile(input: UploadInput!): UploadResponse!

  # Complete signed upload
  completeUpload(id: ID!): FileMetadata

  # Delete file
  deleteFile(id: ID!): Boolean!

  # Update file metadata
  updateFileMetadata(id: ID!, tags: [String!], metadata: String): FileMetadata
}
```

### Example Usage

#### 1. Upload File (Signed URL)

```graphql
mutation {
  uploadFile(input: {
    filename: "image.jpg"
    fileType: IMAGE
    mimeType: "image/jpeg"
    size: 1024000
    tags: ["profile", "user"]
    useSignedUpload: true
  }) {
    success
    message
    uploadURL
    file {
      id
      filename
      status
    }
  }
}
```

#### 2. List Files

```graphql
query {
  files(
    filter: {
      fileType: IMAGE
      status: READY
      tags: ["profile"]
    }
    pagination: {
      limit: 10
      offset: 0
    }
  ) {
    files {
      id
      filename
      originalName
      fileType
      size
      uploadedAt
      publicURL
      cdnURL
    }
    totalCount
    hasNextPage
  }
}
```

#### 3. Generate Download URL

```graphql
query {
  downloadURL(
    id: "file-id-here"
    urlType: SIGNED
    expiresIn: 3600
  ) {
    success
    url
    expiresAt
  }
}
```

### API Endpoints

#### New API Format (Recommended)

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/cdn-service/graphql/healthz` | GET | Health check |
| `/api/cdn-service/graphql/ping` | GET | Ping endpoint |
| `/api/cdn-service/graphql` | POST | GraphQL API |
| `/api/cdn-service/graphql/playground` | GET | GraphQL Playground |
| `/api/cdn-service/upload` | POST | Direct file upload |

#### Legacy Endpoints (Backward Compatibility)

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/health` | GET | Health check (redirects to new format) |
| `/graphql` | POST | GraphQL API (redirects to new format) |
| `/playground` | GET | GraphQL Playground |
| `/upload` | POST | Direct file upload (redirects to new format) |

## Development

### Available Commands

```bash
# Development
make dev          # Start with hot reload
make run          # Build and run
make build        # Build binary
make test         # Run tests
make test-coverage # Run tests with coverage

# Docker
make docker-build        # Build Docker image
make docker-compose-up   # Start all services
make docker-compose-down # Stop all services

# Database
make db-migrate   # Run migrations
make db-reset     # Reset database

# Code quality
make fmt          # Format code
make lint         # Run linter
```

### Project Guidelines

1. **Code Structure**: Follow clean architecture principles
2. **Error Handling**: Use structured error responses
3. **Logging**: Use structured logging with appropriate levels
4. **Testing**: Write unit tests for all business logic
5. **Documentation**: Keep API documentation up to date

## Makefile Commands

The project includes a comprehensive Makefile with commands synchronized with xbit-agent for consistency.

### Build Commands
```bash
make build              # Build for Linux
make build-local        # Build for local platform
make clean              # Clean build artifacts
```

### GraphQL Commands
```bash
make gqlgen             # Generate GraphQL code
make gqlgen-clean       # Clean and regenerate GraphQL code
make gqlgen-fix         # Fix GraphQL generation issues
make gqlgen-status      # Check GraphQL generation status
make gqlgen-prepare     # Prepare environment for GraphQL generation
```

### Development Commands
```bash
make run                # Run the server locally
make run-local          # Run with local environment
make dev                # Run in development mode
make dev-air            # Run with Air hot reload
make install-deps       # Install dependencies
```

### Database Commands
```bash
make db-diff            # Generate database migration diff
make db-rehash          # Rehash database migrations
make db-apply           # Apply database migrations
make db-apply-docker    # Apply migrations in Docker
make db-apply-atlas     # Apply migrations using Atlas
```

### Testing Commands
```bash
make test               # Run all tests
make test-verbose       # Run tests with verbose output
make test-coverage      # Run tests with coverage report
make test-unit          # Run unit tests only
make test-integration   # Run integration tests only
make test-watch         # Run tests in watch mode
```

### Docker Commands
```bash
make local-up           # Start local environment
make local-down         # Stop local environment
make local-logs         # Show local environment logs
make docker-build       # Build Docker image
make docker-run         # Run Docker container
```

### Utility Commands
```bash
make fix-imports        # Fix Go imports using goimports
make format-code        # Format Go code and fix imports
make debug-build        # Debug build with verbose output
make debug-gqlgen       # Debug GraphQL generation
make help               # Show all available commands
```

## Deployment

### Production Deployment

1. **Build and push Docker image**
   ```bash
   make docker-build
   docker tag xbit-cdn-service:latest your-registry/xbit-cdn-service:latest
   docker push your-registry/xbit-cdn-service:latest
   ```

2. **Deploy with docker-compose**
   ```bash
   # On production server
   docker-compose -f docker-compose.prod.yml up -d
   ```

3. **Or deploy to Kubernetes**
   ```bash
   kubectl apply -f k8s/
   ```

### Environment-specific Configurations

- **Development**: Use docker-compose.yml
- **Staging**: Use docker-compose.staging.yml
- **Production**: Use docker-compose.prod.yml

## Monitoring and Observability

### Health Checks

- **Application**: `GET /health`
- **Database**: Built-in connection health check
- **R2 Storage**: Connection validation on startup

### Metrics

The service exposes metrics for:
- Request count and duration
- File upload/download statistics
- Error rates
- Database connection pool status

### Logging

Structured JSON logging with levels:
- `ERROR`: Application errors
- `WARN`: Warning conditions
- `INFO`: General information
- `DEBUG`: Detailed debug information

## Security

### Authentication

- JWT-based authentication
- Configurable token expiry
- Support for custom claims

### Authorization

- Role-based access control
- File ownership validation
- API rate limiting

### File Security

- File type validation
- Size limits
- Virus scanning (configurable)
- Signed URLs for secure access

## Performance

### Optimizations

- Connection pooling for database
- Redis caching for metadata
- CDN integration for global distribution
- Efficient file streaming

### Scaling

- Horizontal scaling support
- Load balancer ready
- Database read replicas support
- CDN edge caching

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   ```bash
   # Check database status
   make docker-compose-logs postgres

   # Reset database
   make db-reset
   ```

2. **R2 Upload Failed**
   - Verify R2 credentials in .env
   - Check bucket permissions
   - Validate endpoint URL

3. **File Not Found**
   - Check file status in database
   - Verify R2 bucket contents
   - Check CDN configuration

### Debug Mode

Enable debug logging:
```bash
export ENV=development
export LOG_LEVEL=debug
```

## Architecture Synchronization with xbit-agent

This service is designed to maintain architectural consistency with the main xbit-agent service:

### Shared Patterns
- **Makefile Structure**: Synchronized commands and targets
- **Configuration Management**: YAML-based config with Viper
- **Database Migrations**: Atlas migration system
- **GraphQL Code Generation**: gqlgen with similar patterns
- **Authentication**: JWT middleware and handlers
- **Logging**: Zap structured logging
- **Project Structure**: Similar internal organization

### Differences from xbit-agent
- **Single GraphQL API**: No admin/user separation (CDN service is user-focused)
- **No Activity Cashback**: CDN service doesn't include activity tracking
- **No JWT Token Generation**: Focused on file operations
- **No Task Management**: No background task processing

### Maintaining Synchronization
When updating this service, ensure:
1. Makefile commands remain consistent with xbit-agent
2. Configuration structure follows the same patterns
3. Code organization maintains similar structure
4. Documentation stays aligned with xbit-agent standards

## Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

### Development Setup

```bash
# Install development tools
make install-deps

# Setup local environment
make setup-local

# Generate GraphQL code
make gqlgen

# Run tests before committing
make test
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Check the documentation in `/docs`
- Review the troubleshooting section

---

**Made with ❤️ for efficient file management and CDN distribution**