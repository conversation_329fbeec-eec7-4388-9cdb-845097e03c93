package service

import (
	"context"
	"io"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/s3"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-cdn-service/internal/model"
)

// FileRepositoryInterface defines the interface for file repository
type FileRepositoryInterface interface {
	Create(ctx context.Context, file *model.FileMetadata) error
	GetByID(ctx context.Context, id string) (*model.FileMetadata, error)
	Update(ctx context.Context, file *model.FileMetadata) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, filter *model.FileFilter, pagination *model.PaginationInput) (*model.FileListResponse, error)
}

// R2ServiceInterface defines the interface for R2 service
type R2ServiceInterface interface {
	UploadFile(ctx context.Context, key string, body io.Reader, contentType string) error
	GeneratePresignedUploadURL(ctx context.Context, key string, contentType string, expiry time.Duration) (string, error)
	GeneratePresignedDownloadURL(ctx context.Context, key string, expiry time.Duration) (string, error)
	GetPublicURL(key string) string
	DeleteFile(ctx context.Context, key string) error
	FileExists(ctx context.Context, key string) (bool, error)
	GetFileInfo(ctx context.Context, key string) (*s3.HeadObjectOutput, error)
}
