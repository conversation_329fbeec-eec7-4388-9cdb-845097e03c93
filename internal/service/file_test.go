package service

import (
	"context"
	"io"
	"testing"
	"time"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-cdn-service/internal/config"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-cdn-service/internal/model"

	"github.com/aws/aws-sdk-go-v2/service/s3"
)

func TestFileService_ValidateUploadInput(t *testing.T) {
	cfg := &config.Config{
		Upload: config.UploadConfig{
			MaxFileSize:       100 * 1024 * 1024, // 100MB
			AllowedExtensions: []string{"jpg", "jpeg", "png", "gif", "mp4", "mov"},
		},
	}

	service := &FileService{
		config: cfg,
	}

	tests := []struct {
		name    string
		input   *model.UploadInput
		wantErr bool
	}{
		{
			name: "valid image upload",
			input: &model.UploadInput{
				Filename: "test.jpg",
				FileType: model.FileTypeImage,
				MimeType: "image/jpeg",
				Size:     1024 * 1024, // 1MB
			},
			wantErr: false,
		},
		{
			name: "valid video upload",
			input: &model.UploadInput{
				Filename: "test.mp4",
				FileType: model.FileTypeVideo,
				MimeType: "video/mp4",
				Size:     50 * 1024 * 1024, // 50MB
			},
			wantErr: false,
		},
		{
			name: "empty filename",
			input: &model.UploadInput{
				Filename: "",
				FileType: model.FileTypeImage,
				MimeType: "image/jpeg",
				Size:     1024,
			},
			wantErr: true,
		},
		{
			name: "invalid file type",
			input: &model.UploadInput{
				Filename: "test.jpg",
				FileType: "INVALID",
				MimeType: "image/jpeg",
				Size:     1024,
			},
			wantErr: true,
		},
		{
			name: "empty mime type",
			input: &model.UploadInput{
				Filename: "test.jpg",
				FileType: model.FileTypeImage,
				MimeType: "",
				Size:     1024,
			},
			wantErr: true,
		},
		{
			name: "zero size",
			input: &model.UploadInput{
				Filename: "test.jpg",
				FileType: model.FileTypeImage,
				MimeType: "image/jpeg",
				Size:     0,
			},
			wantErr: true,
		},
		{
			name: "file too large",
			input: &model.UploadInput{
				Filename: "test.jpg",
				FileType: model.FileTypeImage,
				MimeType: "image/jpeg",
				Size:     200 * 1024 * 1024, // 200MB
			},
			wantErr: true,
		},
		{
			name: "invalid extension",
			input: &model.UploadInput{
				Filename: "test.exe",
				FileType: model.FileTypeImage,
				MimeType: "application/octet-stream",
				Size:     1024,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := service.validateUploadInput(tt.input)
			if (err != nil) != tt.wantErr {
				t.Errorf("validateUploadInput() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestFileService_GetExtensionFromMimeType(t *testing.T) {
	service := &FileService{}

	tests := []struct {
		mimeType string
		want     string
	}{
		{"image/jpeg", ".jpg"},
		{"image/png", ".png"},
		{"image/gif", ".gif"},
		{"image/webp", ".webp"},
		{"video/mp4", ".mp4"},
		{"video/quicktime", ".mov"},
		{"video/x-msvideo", ".avi"},
		{"video/webm", ".webm"},
		{"application/octet-stream", ""},
		{"", ""},
	}

	for _, tt := range tests {
		t.Run(tt.mimeType, func(t *testing.T) {
			got := service.getExtensionFromMimeType(tt.mimeType)
			if got != tt.want {
				t.Errorf("getExtensionFromMimeType() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFileType_IsValid(t *testing.T) {
	tests := []struct {
		fileType model.FileType
		want     bool
	}{
		{model.FileTypeImage, true},
		{model.FileTypeVideo, true},
		{"INVALID", false},
		{"", false},
	}

	for _, tt := range tests {
		t.Run(string(tt.fileType), func(t *testing.T) {
			got := tt.fileType.IsValid()
			if got != tt.want {
				t.Errorf("FileType.IsValid() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFileStatus_IsValid(t *testing.T) {
	tests := []struct {
		status model.FileStatus
		want   bool
	}{
		{model.FileStatusUploading, true},
		{model.FileStatusProcessing, true},
		{model.FileStatusReady, true},
		{model.FileStatusError, true},
		{"INVALID", false},
		{"", false},
	}

	for _, tt := range tests {
		t.Run(string(tt.status), func(t *testing.T) {
			got := tt.status.IsValid()
			if got != tt.want {
				t.Errorf("FileStatus.IsValid() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestURLType_IsValid(t *testing.T) {
	tests := []struct {
		urlType model.URLType
		want    bool
	}{
		{model.URLTypePublic, true},
		{model.URLTypeSigned, true},
		{"INVALID", false},
		{"", false},
	}

	for _, tt := range tests {
		t.Run(string(tt.urlType), func(t *testing.T) {
			got := tt.urlType.IsValid()
			if got != tt.want {
				t.Errorf("URLType.IsValid() = %v, want %v", got, tt.want)
			}
		})
	}
}

// Mock implementations for testing
type mockFileRepository struct {
	files map[string]*model.FileMetadata
}

func (m *mockFileRepository) Create(ctx context.Context, file *model.FileMetadata) error {
	if m.files == nil {
		m.files = make(map[string]*model.FileMetadata)
	}
	file.UploadedAt = time.Now()
	file.UpdatedAt = time.Now()
	m.files[file.ID] = file
	return nil
}

func (m *mockFileRepository) GetByID(ctx context.Context, id string) (*model.FileMetadata, error) {
	if file, exists := m.files[id]; exists {
		return file, nil
	}
	return nil, nil
}

func (m *mockFileRepository) Update(ctx context.Context, file *model.FileMetadata) error {
	if _, exists := m.files[file.ID]; !exists {
		return nil // File not found
	}
	file.UpdatedAt = time.Now()
	m.files[file.ID] = file
	return nil
}

func (m *mockFileRepository) Delete(ctx context.Context, id string) error {
	if _, exists := m.files[id]; !exists {
		return nil // File not found
	}
	delete(m.files, id)
	return nil
}

func (m *mockFileRepository) List(ctx context.Context, filter *model.FileFilter, pagination *model.PaginationInput) (*model.FileListResponse, error) {
	var files []*model.FileMetadata
	for _, file := range m.files {
		files = append(files, file)
	}

	// Simple pagination
	start := pagination.Offset
	end := start + pagination.Limit
	if end > len(files) {
		end = len(files)
	}
	if start > len(files) {
		start = len(files)
	}

	return &model.FileListResponse{
		Files:           files[start:end],
		TotalCount:      len(files),
		HasNextPage:     end < len(files),
		HasPreviousPage: start > 0,
	}, nil
}

type mockR2Service struct{}

func (m *mockR2Service) UploadFile(ctx context.Context, key string, body io.Reader, contentType string) error {
	return nil
}

func (m *mockR2Service) GetPublicURL(key string) string {
	return "https://example.com/" + key
}

func (m *mockR2Service) GeneratePresignedUploadURL(ctx context.Context, key string, contentType string, expiry time.Duration) (string, error) {
	return "https://example.com/upload/" + key, nil
}

func (m *mockR2Service) GeneratePresignedDownloadURL(ctx context.Context, key string, expiry time.Duration) (string, error) {
	return "https://example.com/download/" + key, nil
}

func (m *mockR2Service) DeleteFile(ctx context.Context, key string) error {
	return nil
}

func (m *mockR2Service) FileExists(ctx context.Context, key string) (bool, error) {
	return true, nil
}

func (m *mockR2Service) GetFileInfo(ctx context.Context, key string) (*s3.HeadObjectOutput, error) {
	return &s3.HeadObjectOutput{}, nil
}

// Integration test example
func TestFileService_UploadFile_Integration(t *testing.T) {
	// Setup
	cfg := &config.Config{
		Upload: config.UploadConfig{
			MaxFileSize:       100 * 1024 * 1024,
			AllowedExtensions: []string{"jpg", "jpeg", "png", "gif", "mp4"},
			SignedURLExpiry:   time.Hour,
		},
		CDN: config.CDNConfig{
			BaseURL: "https://cdn.example.com",
		},
	}

	mockRepo := &mockFileRepository{}
	mockR2 := &mockR2Service{}
	mockCDN := (*CDNService)(nil) // CDN service is optional
	service := NewFileService(mockRepo, mockR2, mockCDN, cfg)

	// Test signed upload
	input := &model.UploadInput{
		Filename:        "test.jpg",
		FileType:        model.FileTypeImage,
		MimeType:        "image/jpeg",
		Size:            1024 * 1024,
		Tags:            []string{"test", "image"},
		UseSignedUpload: true,
	}

	ctx := context.Background()
	response, err := service.UploadFile(ctx, input, nil)

	// Assertions
	if err != nil {
		t.Fatalf("UploadFile() error = %v", err)
	}

	if !response.Success {
		t.Errorf("Expected success = true, got %v", response.Success)
	}

	if response.UploadURL == nil {
		t.Error("Expected uploadURL to be set for signed upload")
	}

	if response.File == nil {
		t.Fatal("Expected file metadata to be returned")
	}

	if response.File.Status != model.FileStatusUploading {
		t.Errorf("Expected status = %v, got %v", model.FileStatusUploading, response.File.Status)
	}

	if response.File.PublicURL == nil {
		t.Error("Expected publicURL to be set")
	}

	if response.File.CdnURL == nil {
		t.Error("Expected cdnURL to be set")
	}
}
