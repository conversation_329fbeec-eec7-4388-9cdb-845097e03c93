package graphql

import (
	"context"
	"fmt"
)

type GraphQLHandler struct {
	// fileService *service.FileService
}

func NewGraphQLHandler(fileService interface{}) *GraphQLHandler {
	return &GraphQLHandler{
		// fileService: fileService,
	}
}

// Query resolvers

func (h *GraphQLHandler) Health(ctx context.Context) (string, error) {
	return "OK", nil
}

func (h *GraphQLHandler) File(ctx context.Context, id string) (interface{}, error) {
	return nil, fmt.Errorf("not implemented")
}

func (h *GraphQLHandler) Files(ctx context.Context, filter interface{}, pagination interface{}) (interface{}, error) {
	return nil, fmt.Errorf("not implemented")
}

func (h *GraphQLHandler) DownloadURL(ctx context.Context, id string, urlType interface{}, expiresIn interface{}) (interface{}, error) {
	return nil, fmt.<PERSON><PERSON>rf("not implemented")
}

// Mutation resolvers

func (h *GraphQLHandler) UploadFile(ctx context.Context, input interface{}) (interface{}, error) {
	return nil, fmt.Errorf("not implemented")
}

func (h *GraphQLHandler) CompleteUpload(ctx context.Context, id string) (interface{}, error) {
	return nil, fmt.Errorf("not implemented")
}

func (h *GraphQLHandler) DeleteFile(ctx context.Context, id string) (bool, error) {
	return false, fmt.Errorf("not implemented")
}

func (h *GraphQLHandler) UpdateFileMetadata(ctx context.Context, id string, tags []string, metadata *string) (interface{}, error) {
	return nil, fmt.Errorf("not implemented")
}
