# Local Development Environment Variables
ENV=local

# Server Configuration
# Viper expects SYSTEM_* environment variables for system config
SYSTEM_ENV=local
SYSTEM_ADDR=8080
SYSTEM_ROUTER_PREFIX=/api/cdn
SYSTEM_GRAPHQL_PREFIX=/api/cdn/graphql

# Database Configuration
# Viper expects PGSQL_* environment variables for database config
PGSQL_PATH=localhost
PGSQL_PORT=5433
PGSQL_USERNAME=postgres
PGSQL_PASSWORD=postgres
PGSQL_DB_NAME=xbit_cdn
PGSQL_CONFIG=sslmode=disable
PGSQL_MAX_IDLE_CONNS=10
PGSQL_MAX_OPEN_CONNS=100

# Cloudflare R2 Configuration (Local - use test credentials or leave empty)
# Viper expects R2_* environment variables for R2 config
R2_ACCOUNT_ID=23730d7e6dec9dfe0266e9df554e20c5
R2_ACCESS_KEY_ID=659727ae7f867e3a8430d26d8200b6dc
R2_SECRET_ACCESS_KEY=3a91eb858bd3335ba0c7855fec0322794171a38d1aaec5794f931b0c489faa50
R2_BUCKET_NAME=xbit-unstable
R2_REGION=auto
R2_ENDPOINT=https://23730d7e6dec9dfe0266e9df554e20c5.r2.cloudflarestorage.com

# Cloudflare CDN Configuration (Local)
CDN_BASE_URL=http://localhost:8080
CDN_ZONE_ID=your_test_zone_id
CDN_API_TOKEN=your_test_api_token

# JWT Configuration
# Viper expects JWT_* environment variables for JWT config
JWT_SIGNING_KEY=local-development-secret-key-change-in-production
JWT_EXPIRES_TIME=24h
JWT_BUFFER_TIME=168h
JWT_ISSUER=xbit-cdn-service

# File Upload Configuration
MAX_FILE_SIZE=100MB
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,mp4,mov,avi,webm,pdf,doc,docx
SIGNED_URL_EXPIRY=1h
TEMP_DIR=/tmp/xbit-uploads

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_ENABLED=true

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=*
CORS_ALLOW_CREDENTIALS=true

# Zap Logging Configuration
# Viper expects ZAP_* environment variables for logging config
ZAP_LEVEL=debug
ZAP_FORMAT=console
ZAP_PREFIX=[xbit-cdn-service]
ZAP_DIRECTOR=log
ZAP_ENCODE_LEVEL=LowercaseColorLevelEncoder
ZAP_STACKTRACE_KEY=stacktrace
ZAP_MAX_AGE=0
ZAP_SHOW_LINE=true
ZAP_LOG_IN_CONSOLE=true

# Monitoring Configuration
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true
PROFILING_ENABLED=true

# Security Configuration
ENABLE_HTTPS=false
HSTS_ENABLED=false
CSRF_PROTECTION=false

# Rate Limiting
RATE_LIMITING_ENABLED=false
REQUESTS_PER_MINUTE=1000
BURST_SIZE=100
