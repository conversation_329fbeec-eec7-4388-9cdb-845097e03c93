# XBIT CDN Service - Production Environment Configuration Template
# Copy this file to .env.production and update the values

# Application Configuration
APP_ENV=production
SERVER_PORT=8080

# Database Configuration
POSTGRES_DB=cdn
POSTGRES_AGENCY_HOST=
POSTGRES_AGENCY_PORT=5432
POSTGRES_AGENCY_USER=
POSTGRES_AGENCY_PASS=
POSTGRES_AGENCY_SSL_MODE=require

# JWT Configuration
JWT_SECRET=
JWT_EXPIRES_TIME=7d
JWT_BUFFER_TIME=1d
JWT_ISSUER=xbit-cdn-service

# Cloudflare R2 Configuration
R2_ACCOUNT_ID=
R2_ACCESS_KEY_ID=
R2_SECRET_ACCESS_KEY=
R2_BUCKET_NAME=
R2_REGION=auto
R2_ENDPOINT=

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
