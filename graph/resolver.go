package graph

import (
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-cdn-service/internal/service"
)

// This file will not be regenerated automatically.
//
// It serves as dependency injection for your app, add any dependencies you require here.

type Resolver struct {
	FileService *service.FileService
}

func NewResolver(fileService *service.FileService) *Resolver {
	return &Resolver{
		FileService: fileService,
	}
}
