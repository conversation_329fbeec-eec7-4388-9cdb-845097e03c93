package graph

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.78

import (
	"context"
	"time"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-cdn-service/internal/model"
)

// UploadedAt is the resolver for the uploadedAt field.
func (r *fileMetadataResolver) UploadedAt(ctx context.Context, obj *model.FileMetadata) (string, error) {
	return obj.UploadedAt.Format(time.RFC3339), nil
}

// UpdatedAt is the resolver for the updatedAt field.
func (r *fileMetadataResolver) UpdatedAt(ctx context.Context, obj *model.FileMetadata) (string, error) {
	return obj.UpdatedAt.Format(time.RFC3339), nil
}

// UploadFile is the resolver for the uploadFile field.
func (r *mutationResolver) UploadFile(ctx context.Context, input model.UploadInput) (*model.UploadResponse, error) {
	// Extract user ID from context if available
	var userID *string
	if userCtx := ctx.Value("user"); userCtx != nil {
		if user, ok := userCtx.(map[string]interface{}); ok {
			if uid, exists := user["user_id"].(string); exists {
				userID = &uid
			}
		}
	}

	return r.FileService.UploadFile(ctx, &input, userID)
}

// CompleteUpload is the resolver for the completeUpload field.
func (r *mutationResolver) CompleteUpload(ctx context.Context, id string) (*model.FileMetadata, error) {
	return r.FileService.CompleteUpload(ctx, id)
}

// DeleteFile is the resolver for the deleteFile field.
func (r *mutationResolver) DeleteFile(ctx context.Context, id string) (bool, error) {
	return r.FileService.DeleteFile(ctx, id)
}

// UpdateFileMetadata is the resolver for the updateFileMetadata field.
func (r *mutationResolver) UpdateFileMetadata(ctx context.Context, id string, tags []string, metadata *string) (*model.FileMetadata, error) {
	return r.FileService.UpdateFileMetadata(ctx, id, tags, metadata)
}

// File is the resolver for the file field.
func (r *queryResolver) File(ctx context.Context, id string) (*model.FileMetadata, error) {
	return r.FileService.GetFile(ctx, id)
}

// Files is the resolver for the files field.
func (r *queryResolver) Files(ctx context.Context, filter *model.FileFilter, pagination *model.PaginationInput) (*model.FileListResponse, error) {
	return r.FileService.ListFiles(ctx, filter, pagination)
}

// DownloadURL is the resolver for the downloadURL field.
func (r *queryResolver) DownloadURL(ctx context.Context, id string, urlType *model.URLType, expiresIn *int) (*model.DownloadResponse, error) {
	// Set default URL type if not provided
	urlTypeValue := model.URLTypePublic
	if urlType != nil {
		urlTypeValue = *urlType
	}

	return r.FileService.GenerateDownloadURL(ctx, id, urlTypeValue, expiresIn)
}

// Health is the resolver for the health field.
func (r *queryResolver) Health(ctx context.Context) (string, error) {
	return "OK", nil
}

// FileMetadata returns FileMetadataResolver implementation.
func (r *Resolver) FileMetadata() FileMetadataResolver { return &fileMetadataResolver{r} }

// Mutation returns MutationResolver implementation.
func (r *Resolver) Mutation() MutationResolver { return &mutationResolver{r} }

// Query returns QueryResolver implementation.
func (r *Resolver) Query() QueryResolver { return &queryResolver{r} }

type fileMetadataResolver struct{ *Resolver }
type mutationResolver struct{ *Resolver }
type queryResolver struct{ *Resolver }
