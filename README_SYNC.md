# XBIT CDN Service - Synchronized with xbit-agent

This document describes the synchronization of xbit-cdn-service with xbit-agent structure and conventions.

## 🚀 Quick Start

### Prerequisites
- Go 1.21 or later
- Docker and Docker Compose
- Make

### Setup
```bash
# Run the setup script
./scripts/setup.sh --with-db

# Or setup without database
./scripts/setup.sh
```

### Development
```bash
# Start development server with hot reload
make dev

# Or run the application
make run

# Build the application
make build-local
```

## 📁 Project Structure (Synchronized with xbit-agent)

```
xbit-cdn-service/
├── cmd/
│   ├── server/
│   │   └── main.go
│   └── atlasloader/
│       └── main.go
├── config/
│   ├── config.go
│   ├── database.go
│   ├── jwt.go
│   ├── r2.go
│   ├── system.go
│   └── zap.go
├── internal/
│   ├── app/
│   ├── controller/
│   │   └── graphql/
│   ├── initialize/
│   ├── initializer/
│   ├── middleware/
│   ├── model/
│   ├── repo/
│   ├── service/
│   └── utils/
├── graph/
│   ├── schema.graphqls
│   ├── generated.go
│   ├── resolver.go
│   └── model/
│       └── models_gen.go
├── scripts/
│   ├── run.sh
│   ├── load-env.sh
│   └── setup.sh
├── env/
│   ├── local.env.template
│   └── production.env.template
├── migrations/
├── config.yaml
├── gqlgen.yml
├── atlas.hcl
├── .air.toml
├── Makefile
├── Dockerfile
└── docker-compose.yml
```

## 🔧 Key Changes Made

### 1. Makefile Synchronization
- Added comprehensive build targets (`build`, `build-local`)
- Added GraphQL generation targets (`gqlgen`, `gqlgen-clean`, `gqlgen-fix`)
- Added database migration targets (`db-diff`, `db-apply`, `db-apply-atlas`)
- Added development targets (`dev`, `dev-air`)
- Added testing targets (`test`, `test-verbose`, `test-coverage`)
- Added code formatting targets (`format-code`, `fix-imports`)
- Added debug targets (`debug-build`, `debug-gqlgen`)

### 2. Configuration Structure
- Created modular config system in `config/` directory
- Separated concerns: `database.go`, `jwt.go`, `r2.go`, `system.go`, `zap.go`
- Added `config.yaml` template with environment variable support
- Created environment templates in `env/` directory

### 3. Scripts and Automation
- Added `scripts/run.sh` for environment-based execution
- Added `scripts/load-env.sh` for environment variable loading
- Added `scripts/setup.sh` for automated setup
- Made all scripts executable

### 4. Module Path Update
- Updated `go.mod` to use GitLab module path: `gitlab.ggwp.life/xbit/xbit-dex/xbit-cdn-service`
- Updated all internal imports to use the new module path

### 5. Directory Structure Alignment
- Reorganized `internal/` directory to match xbit-agent structure
- Moved `handler/` to `controller/graphql/`
- Moved `repository/` to `repo/`
- Added missing directories: `app/`, `initialize/`, `initializer/`, `middleware/`, `utils/`

### 6. GraphQL Configuration
- Updated `gqlgen.yml` with proper module path
- Added Atlas migration support with `atlas.hcl`
- Created `cmd/atlasloader/` for database schema generation

### 7. Docker and Environment
- Updated `Dockerfile` to use new binary name
- Updated `docker-compose.yml` with new environment variables
- Added `.air.toml` for hot reload development
- Created environment templates for different stages

## 🛠 Available Commands

### Build Commands
```bash
make build              # Build for Linux
make build-local        # Build for local platform
make clean              # Clean build artifacts
```

### Development Commands
```bash
make dev                # Start development server with hot reload
make run                # Run the application
make dev-air            # Run with air hot reload
```

### GraphQL Commands
```bash
make gqlgen             # Generate GraphQL code
make gqlgen-clean       # Clean and regenerate GraphQL code
make gqlgen-fix         # Fix GraphQL generation issues
```

### Database Commands
```bash
make db-diff            # Generate migration diff
make db-apply           # Apply migrations
make db-apply-atlas     # Apply migrations using Atlas
```

### Testing Commands
```bash
make test               # Run all tests
make test-verbose       # Run tests with verbose output
make test-coverage      # Run tests with coverage report
make test-unit          # Run unit tests only
```

### Code Quality Commands
```bash
make format-code        # Format Go code and fix imports
make fix-imports        # Fix Go imports
make debug-build        # Debug build with verbose output
```

### Docker Commands
```bash
make local-up           # Start local environment
make local-down         # Stop local environment
make local-logs         # Show local environment logs
```

## 🔄 Environment Management

### Local Development
```bash
# Copy environment template
cp env/local.env.template .env.local

# Update configuration
vim .env.local

# Start services
make local-up

# Run migrations
make db-apply

# Start development
make dev
```

### Production Deployment
```bash
# Copy production template
cp env/production.env.template .env.production

# Update configuration
vim .env.production

# Build and deploy
make build
docker build -t xbit-cdn-service .
```

## 📋 Migration from Old Structure

If you have existing code using the old structure, you'll need to update:

1. **Import paths**: Update all imports to use the new module path
2. **Handler references**: Update references from `handler/` to `controller/graphql/`
3. **Repository references**: Update references from `repository/` to `repo/`
4. **Configuration**: Update configuration loading to use the new config structure

## 🎯 Benefits of Synchronization

1. **Consistency**: Both services now follow the same patterns and conventions
2. **Maintainability**: Easier to maintain and understand both codebases
3. **Developer Experience**: Developers can work on both services with familiar patterns
4. **Automation**: Comprehensive Makefile targets for common tasks
5. **Environment Management**: Consistent environment setup across services
6. **Code Quality**: Standardized formatting, testing, and debugging tools

## 🚨 Important Notes

- The old `internal/config/config.go` has been moved to the new config structure
- GraphQL resolvers are now in `internal/controller/graphql/`
- Repository interfaces are now in `internal/repo/`
- All scripts are executable and ready to use
- Environment templates provide a starting point for configuration

## 📞 Support

For questions or issues with the synchronization, please refer to the xbit-agent documentation or contact the development team.
