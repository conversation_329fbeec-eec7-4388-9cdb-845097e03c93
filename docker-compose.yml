version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: xbit-cdn-postgres
    environment:
      POSTGRES_DB: cdn
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d
    networks:
      - xbit-cdn-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis (for caching - optional)
  redis:
    image: redis:7-alpine
    container_name: xbit-cdn-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - xbit-cdn-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # XBIT CDN Service
  xbit-cdn-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: xbit-cdn-service
    ports:
      - "8080:8080"
    environment:
      # Server Configuration
      SERVER_PORT: 8080
      APP_ENV: local

      # Database Configuration
      POSTGRES_AGENCY_HOST: postgres
      POSTGRES_AGENCY_PORT: 5432
      POSTGRES_AGENCY_USER: postgres
      POSTGRES_AGENCY_PASS: postgres
      POSTGRES_DB: cdn
      POSTGRES_AGENCY_SSL_MODE: disable

      # Cloudflare R2 Configuration (set these in .env file)
      R2_ACCOUNT_ID: ${R2_ACCOUNT_ID}
      R2_ACCESS_KEY_ID: ${R2_ACCESS_KEY_ID}
      R2_SECRET_ACCESS_KEY: ${R2_SECRET_ACCESS_KEY}
      R2_BUCKET_NAME: ${R2_BUCKET_NAME}
      R2_ENDPOINT: ${R2_ENDPOINT}

      # Cloudflare CDN Configuration
      CDN_BASE_URL: ${CDN_BASE_URL}
      CDN_ZONE_ID: ${CDN_ZONE_ID}
      CDN_API_TOKEN: ${CDN_API_TOKEN}

      # JWT Configuration
      JWT_SECRET: ${JWT_SECRET:-default-secret-key}
      JWT_EXPIRY: 24h

      # File Upload Configuration
      MAX_FILE_SIZE: 100MB
      ALLOWED_EXTENSIONS: jpg,jpeg,png,gif,mp4,mov,avi,webm
      SIGNED_URL_EXPIRY: 1h
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - xbit-cdn-network
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  # Nginx (reverse proxy - optional)
  nginx:
    image: nginx:alpine
    container_name: xbit-cdn-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - xbit-cdn-service
    networks:
      - xbit-cdn-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  xbit-cdn-network:
    driver: bridge
