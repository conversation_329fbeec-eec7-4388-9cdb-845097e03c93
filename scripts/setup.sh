#!/bin/bash

# XBIT CDN Service Setup Script
# This script helps set up the development environment

set -e

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check Go version
check_go_version() {
    if ! command_exists go; then
        print_error "Go is not installed. Please install Go 1.21 or later."
        exit 1
    fi

    GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
    REQUIRED_VERSION="1.21"

    if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$GO_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
        print_error "Go version $GO_VERSION is too old. Please install Go $REQUIRED_VERSION or later."
        exit 1
    fi

    print_success "Go version $GO_VERSION is compatible"
}

# Function to install dependencies
install_dependencies() {
    print_status "Installing Go dependencies..."
    cd "$PROJECT_DIR"
    go mod tidy
    go mod download
    print_success "Dependencies installed"
}

# Function to install development tools
install_dev_tools() {
    print_status "Installing development tools..."

    # Install air for hot reload
    if ! command_exists air; then
        print_status "Installing air..."
        go install github.com/cosmtrek/air@latest
        print_success "Air installed"
    else
        print_success "Air already installed"
    fi

    # Install gqlgen
    if ! command_exists gqlgen; then
        print_status "Installing gqlgen..."
        go install github.com/99designs/gqlgen@latest
        print_success "Gqlgen installed"
    else
        print_success "Gqlgen already installed"
    fi

    # Install goimports
    if ! command_exists goimports; then
        print_status "Installing goimports..."
        go install golang.org/x/tools/cmd/goimports@latest
        print_success "Goimports installed"
    else
        print_success "Goimports already installed"
    fi
}

# Function to setup environment files
setup_environment() {
    print_status "Setting up environment files..."

    # Create .env.local if it doesn't exist
    if [ ! -f "$PROJECT_DIR/.env.local" ]; then
        if [ -f "$PROJECT_DIR/env/local.env.template" ]; then
            cp "$PROJECT_DIR/env/local.env.template" "$PROJECT_DIR/.env.local"
            print_success "Created .env.local from template"
            print_warning "Please update .env.local with your configuration"
        else
            print_error "Environment template not found"
        fi
    else
        print_success ".env.local already exists"
    fi
}

# Function to generate GraphQL code
generate_graphql() {
    print_status "Generating GraphQL code..."
    cd "$PROJECT_DIR"
    make gqlgen
    print_success "GraphQL code generated"
}

# Function to setup database
setup_database() {
    print_status "Setting up database..."

    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        print_warning "Docker is not running. Please start Docker and run 'make local-up' to start the database."
        return
    fi

    # Start database
    print_status "Starting database..."
    make local-up

    # Wait for database to be ready
    print_status "Waiting for database to be ready..."
    sleep 10

    # Run migrations
    print_status "Running database migrations..."
    make db-apply

    print_success "Database setup complete"
}

# Function to build the application
build_application() {
    print_status "Building application..."
    cd "$PROJECT_DIR"
    make build-local
    print_success "Application built successfully"
}

# Function to run tests
run_tests() {
    print_status "Running tests..."
    cd "$PROJECT_DIR"
    make test
    print_success "Tests completed"
}

# Main setup function
main() {
    echo "=========================================="
    echo "  XBIT CDN Service Setup"
    echo "=========================================="
    echo

    # Check prerequisites
    print_status "Checking prerequisites..."
    check_go_version

    # Install dependencies
    install_dependencies

    # Install development tools
    install_dev_tools

    # Setup environment
    setup_environment

    # Generate GraphQL code
    generate_graphql

    # Setup database (optional)
    if [ "$1" = "--with-db" ]; then
        setup_database
    else
        print_warning "Skipping database setup. Use --with-db to include database setup."
    fi

    # Build application
    build_application

    # Run tests
    run_tests

    echo
    echo "=========================================="
    print_success "Setup completed successfully!"
    echo "=========================================="
    echo
    echo "Next steps:"
    echo "1. Update .env.local with your configuration"
    echo "2. If you didn't setup database, run: make local-up"
    echo "3. Start development server: make dev"
    echo "4. Or run the application: make run"
    echo
    echo "Available commands:"
    echo "  make help          - Show all available commands"
    echo "  make dev           - Start development server with hot reload"
    echo "  make run           - Run the application"
    echo "  make test          - Run tests"
    echo "  make build         - Build the application"
    echo
}

# Run main function with all arguments
main "$@"
